<template>
  <div class="table-container-wrapper">
    <!-- 表格容器 -->
    <div class="table-wrapper">
      <div class="table-container" ref="tableContainer" :style="tableContainerStyle">
        <div class="table-scroll-container">
          <table ref="editableTable" :style="tableStyle" @contextmenu="handleContextMenu">
          <!-- 动态表头 -->
          <tr v-for="(headerRow, headerRowIndex) in currentHeaderConfig.headers" :key="'header-' + headerRowIndex">
            <td
              v-for="(headerCell, headerCellIndex) in headerRow"
              :key="'header-' + headerRowIndex + '-' + headerCellIndex"
              v-show="!isHeaderCellHidden(headerRowIndex, headerCellIndex)"
              class="header-cell"
              :rowspan="getHeaderCellRowspan(headerRowIndex, headerCellIndex)"
              :colspan="getHeaderCellColspan(headerRowIndex, headerCellIndex)"
              :title="getHeaderCellTitle(headerRowIndex, headerCellIndex)"
              :style="getHeaderCellStyle(headerRowIndex, headerCellIndex)"
              :class="getHeaderCellClass(headerRowIndex, headerCellIndex)"
            >
              <span
                v-if="shouldUseVerticalText(headerCellIndex)"
                class="vertical-text-span"
              >
                {{ getHeaderCellContent(headerRowIndex, headerCellIndex) }}
              </span>
              <span v-else>
                {{ getHeaderCellContent(headerRowIndex, headerCellIndex) }}
              </span>
            </td>
          </tr>
          <!-- 数据行 - 可编辑 -->
          <tr v-for="(row, rowIndex) in dataRows" :key="rowIndex">
            <td
              v-for="(cell, cellIndex) in row"
              :key="cellIndex"
              v-show="!isCellHidden(rowIndex, cellIndex)"
              class="editable-cell"
              :class="{
                'has-math': cell.hasMath,
                'merged-cell': cell.merged
              }"
              :style="getMergedCellStyle(rowIndex, cellIndex)"
              :rowspan="getCellRowspan(cell)"
              :colspan="getCellColspan(cell)"
              @contextmenu="handleCellContextMenu(rowIndex, cellIndex, $event)"
            >
              <CellEditor
                :ref="`cell-${rowIndex}-${cellIndex}`"
                :content="cell.content"
                :has-math="cell.hasMath"
                :auto-focus="cell.isEditing"
                :select-all="cell.selectAll"
                :height="getCellEditorHeight(rowIndex, cellIndex)"
                :min-height="getCellEditorMinHeight(rowIndex, cellIndex)"
                @start-edit="handleCellStartEdit(rowIndex, cellIndex)"
                @finish-edit="handleCellFinishEdit(rowIndex, cellIndex, $event)"
                @cancel-edit="handleCellCancelEdit(rowIndex, cellIndex)"
                @content-change="handleCellContentChange(rowIndex, cellIndex, $event)"
                @input="handleCellInput(rowIndex, cellIndex, $event)"
                @move-next="handleCellMoveNext(rowIndex, cellIndex, $event)"
              />
            </td>
          </tr>
        </table>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="context-menu"
      :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
    >
      <div class="context-menu-info">
        <span class="info-label">当前列宽:</span>
        <span class="info-value">{{ getCurrentColumnWidthForDisplay() }}px</span>
      </div>
      <div class="context-menu-divider"></div>
      <div
        class="context-menu-item"
        @click="handleRowHeightClick"
        @mousedown.stop
      >
        调整此行高度
      </div>
      <div
        class="context-menu-item"
        @click="handleColumnWidthClick"
        @mousedown.stop
      >
        调整此列宽度
      </div>
      <div class="context-menu-divider"></div>
      <div
        v-if="isDataRow"
        class="context-menu-item delete-item"
        @click="handleDeleteRowClick"
        @mousedown.stop
      >
        删除此行
      </div>
    </div>

    <!-- 行高调整对话框 -->
    <div v-if="rowHeightDialogVisible" class="dialog-overlay" @click="closeRowHeightDialog">
      <div class="dialog" @click.stop>
        <h3>调整行高度</h3>
        <div class="dialog-content">
          <label>当前行高度: {{ currentRowHeight }}px</label>
          <input
            type="number"
            v-model="newRowHeight"
            :min="minCellHeight"
            placeholder="输入新的行高度(px)"
            @keydown.enter="applyRowHeight"
            @keydown.esc="closeRowHeightDialog"
            ref="rowHeightInput"
          >
        </div>
        <div class="dialog-buttons">
          <button @click="closeRowHeightDialog" class="btn-cancel">取消</button>
          <button @click="applyRowHeight" class="btn-confirm">确定</button>
        </div>
      </div>
    </div>

    <!-- 列宽调整对话框 -->
    <div v-if="columnWidthDialogVisible" class="dialog-overlay" @click="closeColumnWidthDialog">
      <div class="dialog" @click.stop>
        <h3>调整列宽度</h3>
        <div class="dialog-content">
          <label>当前列宽度: {{ currentColumnWidth }}px</label>
          <input
            type="number"
            v-model="newColumnWidth"
            :min="minCellWidth"
            placeholder="输入新的列宽度(px)"
            @keydown.enter="applyColumnWidth"
            @keydown.esc="closeColumnWidthDialog"
            ref="columnWidthInput"
          >
        </div>
        <div class="dialog-buttons">
          <button @click="closeColumnWidthDialog" class="btn-cancel">取消</button>
          <button @click="applyColumnWidth" class="btn-confirm">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CellEditor from './CellEditor.vue'

export default {
  name: 'TableContainer',
  components: {
    CellEditor
  },
  props: {
    // 表格尺寸
    tableWidth: {
      type: String,
      default: '1600px'
    },
    tableHeight: {
      type: String,
      default: '600px'
    },
    // 表格数据
    dataRows: {
      type: Array,
      default: () => [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ]
    },
    // 最小尺寸限制
    minCellWidth: {
      type: Number,
      default: 20
    },
    minCellHeight: {
      type: Number,
      default: 20
    },
    // 表头单元格默认配置
    headerCellWidth: {
      type: Number,
      default: 120
    },
    headerCellHeight: {
      type: Number,
      default: 50
    },
    // 每列的宽度配置（数组，默认8列，支持动态调整）
    columnWidths: {
      type: Array,
      default: () => [150, 200, 150, 50, 50, 80, 80, 80]
    },
    // 表头文字纵向显示配置（数组，默认8列，支持动态调整）
    verticalHeaders: {
      type: Array,
      default: () => [false, false, false, false, false, true, true, true]
    },
    // 动态表头配置（JSON格式）
    headerConfig: {
      type: Object,
      default: null
    },
    // 表头宽度配置（JSON格式）
    headerWidthConfig: {
      type: Object,
      default: null
    },
    // 是否使用动态表头
    useDynamicHeader: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 右键菜单状态
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      currentContextCell: null,
      isDataRow: false,
      currentRowIndex: -1,
      currentColumnIndex: -1,
      contextMenuColumnIndex: -1, // 右键菜单显示的列索引

      // 行高调整对话框
      rowHeightDialogVisible: false,
      currentRowHeight: 0,
      newRowHeight: 0,

      // 列宽调整对话框
      columnWidthDialogVisible: false,
      currentColumnWidth: 0,
      newColumnWidth: 0,

      // 内部列宽状态
      internalColumnWidths: [],

      // 内部动态表头状态（避免直接修改props）
      internalUseDynamicHeader: false,
      internalHeaderConfig: null,
      internalHeaderWidthConfig: null,
      internalDataRows: null,

      // MathJax相关
      mathJaxReady: false
    }
  },
  computed: {
    tableContainerStyle() {
      const style = {
        width: this.tableWidth
      }

      if (this.tableHeight !== 'auto') {
        style.height = this.tableHeight
        style.maxHeight = this.tableHeight
      }

      return style
    },
    tableStyle() {
      // 计算表格总宽度
      const totalWidth = this.currentColumnWidths.reduce((sum, width) => sum + width, 0)
      return {
        width: `${totalWidth}px`,
        height: 'auto'
      }
    },
    // 获取当前使用的列宽数组
    currentColumnWidths() {
      console.log('currentColumnWidths计算 - 开始:', {
        internalColumnWidthsLength: this.internalColumnWidths.length,
        internalColumnWidths: this.internalColumnWidths,
        currentHeaderWidthConfig: this.currentHeaderWidthConfig,
        columnWidths: this.columnWidths
      })

      // 优先使用内部调整的列宽
      if (this.internalColumnWidths.length > 0) {
        console.log('currentColumnWidths使用内部调整配置:', this.internalColumnWidths)
        return this.internalColumnWidths
      }

      // 然后使用动态表头配置的列宽
      if (this.currentHeaderWidthConfig && this.currentHeaderWidthConfig.columnWidths) {
        console.log('currentColumnWidths使用动态配置:', this.currentHeaderWidthConfig.columnWidths)
        return this.currentHeaderWidthConfig.columnWidths
      }

      // 最后使用props的默认列宽
      console.log('currentColumnWidths使用默认配置:', this.columnWidths)
      return this.columnWidths
    },
    // 获取当前使用的表头配置
    currentHeaderConfig() {
      // 优先使用内部状态，然后是props，最后是默认值
      const useDynamic = this.internalUseDynamicHeader || this.useDynamicHeader
      const headerConfig = this.internalHeaderConfig || this.headerConfig

      if (useDynamic && headerConfig) {
        return headerConfig
      }
      // 返回默认表头配置
      return {
        headers: [
          ['检查项目', '技术要求', '检查结果', '完工', '', '检查员', '组长', '检验员'],
          ['', '', '', '月', '日', '', '', '']
        ],
        merges: [
          { startRow: 0, startCol: 0, endRow: 1, endCol: 0, content: '检查项目' },
          { startRow: 0, startCol: 1, endRow: 1, endCol: 1, content: '技术要求' },
          { startRow: 0, startCol: 2, endRow: 1, endCol: 2, content: '检查结果' },
          { startRow: 0, startCol: 3, endRow: 0, endCol: 4, content: '完工' },
          { startRow: 0, startCol: 5, endRow: 1, endCol: 5, content: '检查员' },
          { startRow: 0, startCol: 6, endRow: 1, endCol: 6, content: '组长' },
          { startRow: 0, startCol: 7, endRow: 1, endCol: 7, content: '检验员' }
        ]
      }
    },
    // 获取当前使用的表头宽度配置
    currentHeaderWidthConfig() {
      // 优先使用内部状态，然后是props，最后是默认值
      const useDynamic = this.internalUseDynamicHeader || this.useDynamicHeader
      const widthConfig = this.internalHeaderWidthConfig || this.headerWidthConfig

      console.log('currentHeaderWidthConfig 计算:', {
        internalUseDynamicHeader: this.internalUseDynamicHeader,
        useDynamicHeader: this.useDynamicHeader,
        useDynamic,
        internalHeaderWidthConfig: this.internalHeaderWidthConfig,
        headerWidthConfig: this.headerWidthConfig,
        widthConfig
      })

      if (useDynamic && widthConfig) {
        console.log('使用动态宽度配置:', widthConfig)
        if (!widthConfig.verticalHeaders) {
          widthConfig.verticalHeaders = this.verticalHeaders
        }
        return widthConfig
      }
      // 返回默认宽度配置
      const defaultConfig = {
        columnWidths: [150, 200, 150, 50, 50, 80, 80, 80],
        headerHeights: [50, 50], // 两行表头的高度
        verticalHeaders: [false, false, false, false, false, true, true, true]
      }
      console.log('使用默认宽度配置:', defaultConfig)
      return defaultConfig
    },
    // 获取当前表头的列数
    currentColumnCount() {
      // 从表头配置中获取列数
      if (this.currentHeaderConfig && this.currentHeaderConfig.headers && this.currentHeaderConfig.headers.length > 0) {
        // 取第一行表头的长度作为列数
        const firstHeaderRow = this.currentHeaderConfig.headers[0]
        return firstHeaderRow ? firstHeaderRow.length : 8
      }
      // 默认8列
      return 8
    },
    // 获取数据单元格的样式
    getDataCellStyle() {
      return (columnIndex) => {
        const width = this.currentColumnWidths[columnIndex] || this.headerCellWidth
        return {
          minWidth: `${width}px !important`,
          width: `${width}px !important`,
          maxWidth: `${width}px !important`,
          boxSizing: 'border-box'
        }
      }
    },
    // 获取表头文字的样式类
    getHeaderTextClass() {
      return (columnIndex) => {
        const isVertical = this.currentHeaderWidthConfig.verticalHeaders[columnIndex] || false
        return {
          'vertical-text': isVertical,
          'horizontal-text': !isVertical
        }
      }
    },

    // === 合并单元格相关计算属性 ===

    // 获取合并单元格的样式
    getMergedCellStyle() {
      return (rowIndex, cellIndex) => {
        const baseStyle = this.getDataCellStyle(cellIndex)
        const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]

        if (cell && cell.merged) {
          const { rowspan, colspan } = cell.merged

          // 计算合并后的宽度
          if (colspan > 1) {
            let totalWidth = 0
            for (let i = cellIndex; i < cellIndex + colspan && i < this.currentColumnWidths.length; i++) {
              totalWidth += this.currentColumnWidths[i] || this.headerCellWidth
            }
            baseStyle.minWidth = `${totalWidth}px !important`
            baseStyle.width = `${totalWidth}px !important`
            baseStyle.maxWidth = `${totalWidth}px !important`
          }

          // 计算合并后的高度
          if (rowspan > 1) {
            const baseHeight = 50 // 基础行高
            const totalHeight = baseHeight * rowspan
            baseStyle.minHeight = `${totalHeight}px !important`
            baseStyle.height = `${totalHeight}px !important`
          }

          // 合并单元格的特殊样式
          baseStyle.backgroundColor = '#f8f9fa'
          baseStyle.border = '2px solid #007bff'
          baseStyle.verticalAlign = 'top' // 确保内容从顶部开始
          baseStyle.padding = '0' // 移除内边距，让编辑器占满整个空间
        }

        return baseStyle
      }
    },

    // 获取单元格的rowspan
    getCellRowspan() {
      return (cell) => {
        return cell && cell.merged && cell.merged.rowspan > 1 ? cell.merged.rowspan : null
      }
    },

    // 获取单元格的colspan
    getCellColspan() {
      return (cell) => {
        return cell && cell.merged && cell.merged.colspan > 1 ? cell.merged.colspan : null
      }
    },

    // 判断单元格是否被隐藏（被其他合并单元格覆盖）
    isCellHidden() {
      return (rowIndex, cellIndex) => {
        // 检查是否被其他合并单元格覆盖
        for (let r = 0; r < this.dataRows.length; r++) {
          const row = this.dataRows[r]
          if (!row) continue

          for (let c = 0; c < row.length; c++) {
            const cell = row[c]
            if (cell && cell.merged && !(r === rowIndex && c === cellIndex)) {
              const { startRow, startCol, endRow, endCol } = cell.merged
              if (rowIndex >= startRow && rowIndex <= endRow &&
                  cellIndex >= startCol && cellIndex <= endCol) {
                return true
              }
            }
          }
        }
        return false
      }
    },

    // 获取单元格编辑器的高度
    getCellEditorHeight() {
      return (rowIndex, cellIndex) => {
        const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]
        if (cell && cell.merged) {
          // 合并单元格：计算总高度
          const { rowspan } = cell.merged
          const baseHeight = 50 // 基础行高
          return baseHeight * rowspan
        }
        return 'auto' // 普通单元格使用自动高度
      }
    },

    // 获取单元格编辑器的最小高度
    getCellEditorMinHeight() {
      return (rowIndex, cellIndex) => {
        const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]
        if (cell && cell.merged) {
          // 合并单元格：计算最小高度
          const { rowspan } = cell.merged
          const baseMinHeight = 50 // 基础最小高度
          return baseMinHeight * rowspan
        }
        return 50 // 普通单元格的最小高度
      }
    }
  },
  watch: {
    // 监听 columnWidths prop 的变化
    columnWidths: {
      handler(newWidths) {
        // 只有在没有使用动态表头配置时才更新内部列宽
        if (!this.internalUseDynamicHeader) {
          console.log('columnWidths watch触发，更新internalColumnWidths:', newWidths)
          this.internalColumnWidths = [...newWidths]
        } else {
          console.log('columnWidths watch触发，但使用动态表头配置，忽略更新')
        }
      },
      immediate: true,
      deep: true
    },
    // 监听表头配置变化，自动调整数据行列数
    currentColumnCount: {
      handler(newColumnCount, oldColumnCount) {
        if (oldColumnCount !== undefined && newColumnCount !== oldColumnCount) {
          console.log('表头列数变化:', oldColumnCount, '->', newColumnCount)
          // 调整所有现有数据行的列数
          this.ensureAllRowsColumnCount()
        }
      },
      immediate: false
    }
  },
  mounted() {
    this.setupEventListeners()
    this.initializeMathJax()
    // 初始化内部列宽状态（只有在没有使用动态表头配置时）
    if (!this.internalUseDynamicHeader) {
      console.log('mounted初始化internalColumnWidths:', this.columnWidths)
      this.internalColumnWidths = [...this.columnWidths]
    } else {
      console.log('mounted时使用动态表头配置，不初始化internalColumnWidths')
    }
  },
  beforeDestroy() {
    this.removeEventListeners()
  },
  methods: {
    // ========== 动态表头相关方法 ==========

    /**
     * 设置动态表头配置（避免直接修改props）
     */
    setDynamicHeaderConfig(useDynamic, headerConfig, headerWidthConfig) {
      console.log('设置动态表头配置 - 调用前状态:', {
        oldInternalUseDynamicHeader: this.internalUseDynamicHeader,
        oldInternalHeaderConfig: this.internalHeaderConfig,
        oldInternalHeaderWidthConfig: this.internalHeaderWidthConfig,
        oldInternalColumnWidths: this.internalColumnWidths
      })

      this.internalUseDynamicHeader = useDynamic
      this.internalHeaderConfig = headerConfig
      this.internalHeaderWidthConfig = headerWidthConfig

      // 关键修复：当启用动态表头配置时，清空内部列宽配置
      if (useDynamic) {
        console.log('启用动态表头配置，清空internalColumnWidths')
        this.internalColumnWidths = []
      }

      console.log('设置动态表头配置 - 调用后状态:', {
        useDynamic,
        headerConfig,
        headerWidthConfig,
        newInternalUseDynamicHeader: this.internalUseDynamicHeader,
        newInternalHeaderConfig: this.internalHeaderConfig,
        newInternalHeaderWidthConfig: this.internalHeaderWidthConfig,
        newInternalColumnWidths: this.internalColumnWidths
      })

      // 强制触发computed属性重新计算
      this.$nextTick(() => {
        console.log('nextTick后的currentHeaderWidthConfig:', this.currentHeaderWidthConfig)
        console.log('nextTick后的currentColumnWidths:', this.currentColumnWidths)
        console.log('nextTick后的currentColumnCount:', this.currentColumnCount)

        // 确保所有数据行的列数与新的表头一致
        this.ensureAllRowsColumnCount()

        // 移除$forceUpdate()，避免循环更新
        // this.$forceUpdate()
      })
    },

    /**
     * 检查表头单元格是否被隐藏（被合并覆盖）
     */
    isHeaderCellHidden(headerRowIndex, headerCellIndex) {
      if (!this.currentHeaderConfig.merges) return false

      for (const merge of this.currentHeaderConfig.merges) {
        // 检查当前单元格是否在合并范围内，但不是主单元格
        if (headerRowIndex >= merge.startRow && headerRowIndex <= merge.endRow &&
            headerCellIndex >= merge.startCol && headerCellIndex <= merge.endCol &&
            !(headerRowIndex === merge.startRow && headerCellIndex === merge.startCol)) {
          return true
        }
      }
      return false
    },

    /**
     * 获取表头单元格的行跨度
     */
    getHeaderCellRowspan(headerRowIndex, headerCellIndex) {
      if (!this.currentHeaderConfig.merges) return 1

      for (const merge of this.currentHeaderConfig.merges) {
        if (merge.startRow === headerRowIndex && merge.startCol === headerCellIndex) {
          return merge.endRow - merge.startRow + 1
        }
      }
      return 1
    },

    /**
     * 获取表头单元格的列跨度
     */
    getHeaderCellColspan(headerRowIndex, headerCellIndex) {
      if (!this.currentHeaderConfig.merges) return 1

      for (const merge of this.currentHeaderConfig.merges) {
        if (merge.startRow === headerRowIndex && merge.startCol === headerCellIndex) {
          return merge.endCol - merge.startCol + 1
        }
      }
      return 1
    },

    /**
     * 获取表头单元格的标题
     */
    getHeaderCellTitle(headerRowIndex, headerCellIndex) {
      return this.getHeaderCellContent(headerRowIndex, headerCellIndex)
    },

    /**
     * 获取表头单元格的内容
     */
    getHeaderCellContent(headerRowIndex, headerCellIndex) {
      // 首先检查是否有合并单元格的自定义内容
      if (this.currentHeaderConfig.merges) {
        for (const merge of this.currentHeaderConfig.merges) {
          if (merge.startRow === headerRowIndex && merge.startCol === headerCellIndex && merge.content) {
            return merge.content
          }
        }
      }

      // 返回原始表头内容
      const headerRow = this.currentHeaderConfig.headers[headerRowIndex]
      return headerRow ? (headerRow[headerCellIndex] || '') : ''
    },

    /**
     * 获取表头单元格的样式
     */
    getHeaderCellStyle(headerRowIndex, headerCellIndex) {
      const widthConfig = this.currentHeaderWidthConfig
      const width = widthConfig.columnWidths[headerCellIndex] || 150
      const height = widthConfig.headerHeights[headerRowIndex] || 50

      return {
        minWidth: `${width}px !important`,
        width: `${width}px !important`,
        height: `${height}px !important`,
        minHeight: `${height}px !important`
      }
    },

    /**
     * 获取表头单元格的CSS类
     */
    getHeaderCellClass(headerRowIndex, headerCellIndex) {
      return this.getHeaderTextClass(headerCellIndex)
    },

    /**
     * 判断是否应该使用纵向文字
     */
    shouldUseVerticalText(headerCellIndex) {
      return this.currentHeaderWidthConfig.verticalHeaders[headerCellIndex] || false
    },

    // ========== 原有方法 ==========

    // 设置事件监听器
    setupEventListeners() {
      document.addEventListener('mousedown', this.handleDocumentClick)
    },

    // 移除事件监听器
    removeEventListeners() {
      document.removeEventListener('mousedown', this.handleDocumentClick)
    },

    // 处理右键菜单
    handleContextMenu(e) {
      e.preventDefault()

      const cell = e.target.closest('td')
      if (!cell) return

      this.currentContextCell = cell
      this.contextMenuX = e.pageX
      this.contextMenuY = e.pageY
      this.contextMenuVisible = true

      // 判断是否为数据行（非表头）
      this.isDataRow = cell.classList.contains('editable-cell')

      // 计算当前单元格的行列索引
      this.calculateCellPosition(cell)

      // 获取当前行高和列宽
      this.getCurrentRowHeight()
      this.getCurrentColumnWidth()

      // 调整菜单位置避免超出屏幕
      this.$nextTick(() => {
        this.adjustContextMenuPosition()
      })
    },

    // 计算单元格位置
    calculateCellPosition(cell) {
      const table = this.$refs.editableTable
      if (!table) {
        console.log('calculateCellPosition: table not found')
        return
      }

      const rows = Array.from(table.rows)
      const rowIndex = rows.findIndex(row => Array.from(row.cells).includes(cell))
      this.currentRowIndex = rowIndex

      console.log('calculateCellPosition开始:', {
        cell: cell,
        cellClasses: cell.className,
        rowIndex: rowIndex,
        totalRows: rows.length,
        isHeaderCell: cell.classList.contains('header-cell'),
        isEditableCell: cell.classList.contains('editable-cell')
      })

      if (rowIndex >= 0) {
        let columnIndex = 0
        const row = rows[rowIndex]
        const cells = Array.from(row.cells)
        const cellIndex = cells.indexOf(cell)

        console.log('计算列索引:', {
          cellIndex: cellIndex,
          totalCells: cells.length
        })

        for (let i = 0; i < cellIndex; i++) {
          const colspan = parseInt(cells[i].getAttribute('colspan') || '1')
          columnIndex += colspan
          console.log(`第${i}个单元格colspan=${colspan}, 累计columnIndex=${columnIndex}`)
        }

        this.currentColumnIndex = columnIndex
        this.contextMenuColumnIndex = columnIndex // 同时设置右键菜单的列索引

        console.log(`最终设置: currentColumnIndex=${columnIndex}, contextMenuColumnIndex=${columnIndex}`)
      } else {
        this.currentColumnIndex = 0
        this.contextMenuColumnIndex = 0
        console.log('行索引计算失败，使用默认值0')
      }
    },

    // 获取当前行高度
    getCurrentRowHeight() {
      if (this.currentRowIndex >= 0) {
        const table = this.$refs.editableTable
        if (table && table.rows[this.currentRowIndex]) {
          const row = table.rows[this.currentRowIndex]
          const rect = row.getBoundingClientRect()
          this.currentRowHeight = Math.round(rect.height)
        } else {
          this.currentRowHeight = 50
        }
      } else {
        this.currentRowHeight = 50
      }
    },

    // 获取当前列宽度
    getCurrentColumnWidth() {
      if (this.currentContextCell) {
        const rect = this.currentContextCell.getBoundingClientRect()
        this.currentColumnWidth = Math.round(rect.width)
      } else {
        this.currentColumnWidth = 120
      }
    },

    // 获取当前列宽度用于显示（右键菜单）
    getCurrentColumnWidthForDisplay() {
      console.log('getCurrentColumnWidthForDisplay调用:', {
        contextMenuColumnIndex: this.contextMenuColumnIndex,
        currentRowIndex: this.currentRowIndex,
        currentColumnWidths: this.currentColumnWidths,
        currentColumnWidthsLength: this.currentColumnWidths.length,
        headerCellWidth: this.headerCellWidth,
        currentHeaderConfig: this.currentHeaderConfig
      })

      if (this.contextMenuColumnIndex >= 0 && this.contextMenuColumnIndex < this.currentColumnWidths.length) {
        // 检查是否是表头行，如果是，需要考虑合并单元格
        if (this.currentRowIndex >= 0 && this.currentHeaderConfig && this.currentHeaderConfig.merges) {
          const mergedWidth = this.calculateMergedCellWidth(this.currentRowIndex, this.contextMenuColumnIndex)
          if (mergedWidth > 0) {
            console.log(`获取第${this.contextMenuColumnIndex}列合并宽度: ${mergedWidth}px (行${this.currentRowIndex})`)
            return mergedWidth
          } else if (mergedWidth === -1) {
            // 被合并覆盖的单元格，显示该列的原始宽度
            const width = this.currentColumnWidths[this.contextMenuColumnIndex]
            console.log(`获取第${this.contextMenuColumnIndex}列原始宽度: ${width}px (被合并覆盖)`)
            return width
          }
        }

        // 非合并单元格或数据行，返回单列宽度
        const width = this.currentColumnWidths[this.contextMenuColumnIndex]
        console.log(`获取第${this.contextMenuColumnIndex}列单独宽度: ${width}px`)
        return width
      }
      console.log('获取显示宽度失败，使用默认值:', this.headerCellWidth)
      return this.headerCellWidth
    },

    // 计算合并单元格的宽度
    calculateMergedCellWidth(rowIndex, columnIndex) {
      if (!this.currentHeaderConfig || !this.currentHeaderConfig.merges) {
        return 0
      }

      console.log('calculateMergedCellWidth:', {
        rowIndex,
        columnIndex,
        merges: this.currentHeaderConfig.merges
      })

      // 查找当前单元格是否是合并单元格的主单元格
      for (const merge of this.currentHeaderConfig.merges) {
        if (merge.startRow === rowIndex && merge.startCol === columnIndex) {
          // 这是合并单元格的主单元格，计算总宽度
          let totalWidth = 0
          for (let col = merge.startCol; col <= merge.endCol; col++) {
            if (col < this.currentColumnWidths.length) {
              totalWidth += this.currentColumnWidths[col]
            }
          }

          console.log(`找到合并单元格: 行${rowIndex} 列${merge.startCol}-${merge.endCol}, 总宽度: ${totalWidth}px`)
          return totalWidth
        }
      }

      // 检查当前单元格是否被其他合并单元格覆盖
      for (const merge of this.currentHeaderConfig.merges) {
        if (rowIndex >= merge.startRow && rowIndex <= merge.endRow &&
            columnIndex >= merge.startCol && columnIndex <= merge.endCol &&
            !(merge.startRow === rowIndex && merge.startCol === columnIndex)) {
          // 这个单元格被合并覆盖了，不应该显示
          console.log(`单元格被合并覆盖: 行${rowIndex} 列${columnIndex}`)
          return -1 // 返回-1表示被覆盖
        }
      }

      console.log(`非合并单元格: 行${rowIndex} 列${columnIndex}`)
      return 0 // 返回0表示非合并单元格
    },

    // 调整右键菜单位置
    adjustContextMenuPosition() {
      const menu = document.querySelector('.context-menu')
      if (!menu) return

      const menuRect = menu.getBoundingClientRect()
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      if (this.contextMenuX + menuRect.width > windowWidth) {
        this.contextMenuX = windowWidth - menuRect.width - 10
      }

      if (this.contextMenuY + menuRect.height > windowHeight) {
        this.contextMenuY = windowHeight - menuRect.height - 10
      }
    },

    // 处理文档点击（关闭菜单）
    handleDocumentClick(e) {
      if (!this.contextMenuVisible) {
        return
      }

      if (e.target.closest('.context-menu')) {
        return
      }

      this.contextMenuVisible = false
    },

    // 处理行高点击
    handleRowHeightClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.showRowHeightDialog()
    },

    // 处理列宽点击
    handleColumnWidthClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.showColumnWidthDialog()
    },

    // 处理删除行点击
    handleDeleteRowClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.deleteCurrentRow()
    },

    // 显示行高调整对话框
    showRowHeightDialog() {
      this.contextMenuVisible = false
      this.newRowHeight = this.currentRowHeight || 50
      this.rowHeightDialogVisible = true

      this.$nextTick(() => {
        if (this.$refs.rowHeightInput) {
          this.$refs.rowHeightInput.focus()
          this.$refs.rowHeightInput.select()
        }
      })
    },

    // 显示列宽调整对话框
    showColumnWidthDialog() {
      this.contextMenuVisible = false
      this.newColumnWidth = this.currentColumnWidth || 120
      this.columnWidthDialogVisible = true

      this.$nextTick(() => {
        if (this.$refs.columnWidthInput) {
          this.$refs.columnWidthInput.focus()
          this.$refs.columnWidthInput.select()
        }
      })
    },

    // 关闭行高调整对话框
    closeRowHeightDialog() {
      this.rowHeightDialogVisible = false
    },

    // 关闭列宽调整对话框
    closeColumnWidthDialog() {
      this.columnWidthDialogVisible = false
    },

    // 删除当前行
    deleteCurrentRow() {
      if (this.isDataRow && this.currentRowIndex >= 2) {
        const dataRowIndex = this.currentRowIndex - 2

        if (dataRowIndex >= 0 && dataRowIndex < this.dataRows.length) {
          if (confirm('确定要删除这一行吗？此操作不可撤销。')) {
            this.$emit('delete-row', dataRowIndex)
            this.contextMenuVisible = false
          }
        }
      }
    },

    // 应用行高调整
    applyRowHeight() {
      const height = Math.max(this.minCellHeight, parseInt(this.newRowHeight) || this.minCellHeight)

      if (this.currentRowIndex >= 0) {
        const table = this.$refs.editableTable
        const targetRow = table.rows[this.currentRowIndex]

        const cells = Array.from(targetRow.cells)

        cells.forEach(cell => {
          const rowspan = parseInt(cell.getAttribute('rowspan') || '1')

          if (rowspan > 1) {
            for (let i = 0; i < rowspan; i++) {
              const rowIndex = this.currentRowIndex + i
              if (rowIndex < table.rows.length) {
                this.setRowHeight(table.rows[rowIndex], height / rowspan)
              }
            }
          } else {
            cell.style.height = `${height}px`
            cell.style.minHeight = `${height}px`
          }
        })

        this.adjustCrossRowMergedCells(height)
      }

      this.closeRowHeightDialog()
      this.$emit('table-updated')
    },

    // 设置行高度
    setRowHeight(row, height) {
      const cells = Array.from(row.cells)
      cells.forEach(cell => {
        cell.style.height = `${height}px`
        cell.style.minHeight = `${height}px`
      })
    },

    // 调整跨行合并单元格
    adjustCrossRowMergedCells(newHeight) {
      const table = this.$refs.editableTable
      const rows = Array.from(table.rows)

      for (let i = 0; i < this.currentRowIndex; i++) {
        const row = rows[i]
        const cells = Array.from(row.cells)

        cells.forEach(cell => {
          const rowspan = parseInt(cell.getAttribute('rowspan') || '1')
          if (rowspan > 1 && i + rowspan > this.currentRowIndex) {
            const totalHeight = newHeight * (rowspan - (this.currentRowIndex - i))
            cell.style.height = `${totalHeight}px`
            cell.style.minHeight = `${totalHeight}px`
          }
        })
      }
    },

    // 应用列宽调整
    applyColumnWidth() {
      const newWidth = Math.max(this.minCellWidth, parseInt(this.newColumnWidth) || this.minCellWidth)

      if (this.currentColumnIndex >= 0) {
        // 更新 columnWidths 数组
        this.updateColumnWidth(this.currentColumnIndex, newWidth)
      }

      this.closeColumnWidthDialog()
      this.$emit('table-updated')
    },

    // 更新指定列的宽度
    updateColumnWidth(columnIndex, newWidth) {
      console.log(`更新列宽: 列${columnIndex} 新宽度${newWidth}px`)
      console.log('当前状态:', {
        internalUseDynamicHeader: this.internalUseDynamicHeader,
        internalColumnWidthsLength: this.internalColumnWidths.length,
        currentHeaderWidthConfig: this.currentHeaderWidthConfig
      })

      // 如果使用动态表头配置，需要更新动态配置
      if (this.internalUseDynamicHeader && this.internalHeaderWidthConfig && this.internalHeaderWidthConfig.columnWidths) {
        console.log('更新动态表头配置中的列宽')
        // 确保动态配置的列宽数组存在且长度足够
        if (columnIndex >= 0 && columnIndex < this.internalHeaderWidthConfig.columnWidths.length) {
          // 更新动态配置中的列宽
          this.$set(this.internalHeaderWidthConfig.columnWidths, columnIndex, newWidth)
          console.log('动态配置更新成功:', this.internalHeaderWidthConfig.columnWidths)
        }
      } else {
        // 使用传统的内部列宽更新方式
        console.log('更新内部列宽配置')
        // 确保内部列宽数组有足够的长度
        if (this.internalColumnWidths.length === 0) {
          this.internalColumnWidths = [...this.currentColumnWidths]
        }

        if (columnIndex >= 0 && columnIndex < this.internalColumnWidths.length) {
          this.$set(this.internalColumnWidths, columnIndex, newWidth)
          console.log('内部配置更新成功:', this.internalColumnWidths)
        }
      }

      // 发射事件通知父组件更新
      this.$emit('column-width-changed', {
        columnIndex,
        newWidth,
        columnWidths: [...this.currentColumnWidths],
        useDynamicHeader: this.internalUseDynamicHeader
      })
    },

    // 检测内容是否包含数学公式
    containsMath(content) {
      if (!content) return false

      const mathPatterns = [
        /\$.*?\$/,
        /\$\$.*?\$\$/,
        /\\\(.*?\\\)/,
        /\\\[.*?\\\]/,
        /\\begin\{.*?\}.*?\\end\{.*?\}/,
        /\\[a-zA-Z]+/
      ]

      return mathPatterns.some(pattern => pattern.test(content))
    },

    // 初始化MathJax
    async initializeMathJax() {
      try {
        window.MathJax = {
          tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']]
          },
          chtml: {
            fontURL: '/fonts/mathjax'
          }
        }

        const mathJaxScript = document.createElement('script')
        mathJaxScript.id = 'MathJax-script'
        mathJaxScript.async = true
        mathJaxScript.src = '/js/mathjax/tex-mml-chtml-mathjax-newcm.js'

        mathJaxScript.onload = () => {
          this.mathJaxReady = true
        }

        mathJaxScript.onerror = () => {
          this.mathJaxReady = false
        }

        document.head.appendChild(mathJaxScript)

      } catch (error) {
        console.error('MathJax初始化失败:', error)
        this.mathJaxReady = false
      }
    },

    // 应用统一尺寸设置
    applyUniformSize(width, height) {
      const table = this.$refs.editableTable
      if (table) {
        const cells = table.querySelectorAll('td')

        cells.forEach((cell) => {
          cell.style.width = `${width}px`
          cell.style.minWidth = `${width}px`
          cell.style.height = `${height}px`
          cell.style.minHeight = `${height}px`
        })

        this.$emit('table-updated')
      }
    },

    // 获取表格引用（供父组件使用）
    getTableRef() {
      return this.$refs.editableTable
    },

    // 获取表格容器引用（供父组件使用）
    getTableContainerRef() {
      return this.$refs.tableContainer
    },

    // 获取当前列宽配置（供导出功能使用）
    getColumnWidths() {
      return [...this.currentColumnWidths]
    },

    // 获取指定列的实际宽度
    getActualColumnWidth(columnIndex) {
      if (columnIndex < 0 || columnIndex >= this.currentColumnWidths.length) {
        return this.headerCellWidth
      }
      return this.currentColumnWidths[columnIndex]
    },

    // 获取所有列的实际宽度信息
    getColumnWidthsInfo() {
      return {
        columnWidths: [...this.currentColumnWidths],
        headerCellWidth: this.headerCellWidth,
        headerCellHeight: this.headerCellHeight,
        totalWidth: this.currentColumnWidths.reduce((sum, width) => sum + width, 0),
        verticalHeaders: [...this.verticalHeaders]
      }
    },

    // 获取当前表格的列数
    getCurrentColumnCount() {
      return this.currentColumnCount
    },

    // === 新的 CellEditor 事件处理方法 ===

    // 处理单元格右键菜单
    handleCellContextMenu(rowIndex, cellIndex, event) {
      // 复用原来的右键菜单逻辑
      this.handleContextMenu(event)
    },

    // 处理单元格开始编辑
    handleCellStartEdit(rowIndex, cellIndex) {
      console.log('单元格开始编辑:', rowIndex, cellIndex)

      // 确保行存在
      this.ensureRowExists(rowIndex)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.isEditing = true
        this.$emit('start-edit', { rowIndex, cellIndex, cell })
      }
    },

    // 处理单元格完成编辑
    handleCellFinishEdit(rowIndex, cellIndex, newContent) {
      console.log('单元格完成编辑:', rowIndex, cellIndex, newContent)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.isEditing = false
        cell.content = newContent || ''
        cell.hasMath = this.containsMath(cell.content)

        this.$emit('finish-edit', { rowIndex, cellIndex, cell, newContent })
      }
    },

    // 处理单元格取消编辑
    handleCellCancelEdit(rowIndex, cellIndex) {
      console.log('单元格取消编辑:', rowIndex, cellIndex)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.isEditing = false
        this.$emit('cancel-edit', { rowIndex, cellIndex, cell })
      }
    },

    // 处理单元格内容变化
    handleCellContentChange(rowIndex, cellIndex, newContent) {
      console.log('单元格内容变化:', rowIndex, cellIndex, newContent)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.content = newContent || ''
        cell.hasMath = this.containsMath(cell.content)
      }
    },

    // 处理单元格输入
    handleCellInput(rowIndex, cellIndex, content) {
      // 实时更新内容
      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.content = content || ''
      }
    },

    // 处理单元格移动到下一个
    handleCellMoveNext(rowIndex, cellIndex, direction) {
      console.log('移动到下一个单元格:', direction)

      let nextRowIndex = rowIndex
      let nextCellIndex = cellIndex

      if (direction === 'next') {
        nextCellIndex++
        if (nextCellIndex >= this.currentColumnCount) {
          nextCellIndex = 0
          nextRowIndex++
        }
      } else if (direction === 'prev') {
        nextCellIndex--
        if (nextCellIndex < 0) {
          nextCellIndex = this.currentColumnCount - 1
          nextRowIndex--
        }
      }

      // 确保目标行存在
      if (nextRowIndex >= 0) {
        this.ensureRowExists(nextRowIndex)

        // 聚焦到下一个单元格
        this.$nextTick(() => {
          const nextCellRef = this.$refs[`cell-${nextRowIndex}-${nextCellIndex}`]
          if (nextCellRef && nextCellRef[0]) {
            nextCellRef[0].edit()
          }
        })
      }
    },

    // 确保行存在
    ensureRowExists(rowIndex) {
      while (this.dataRows.length <= rowIndex) {
        const newRow = Array(this.currentColumnCount).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false,
          selectAll: false
        }))
        this.dataRows.push(newRow)
        this.$emit('ensure-row', this.dataRows.length - 1)
      }

      // 确保现有行的列数与当前表头一致
      this.ensureRowColumnCount(rowIndex)
    },

    // 确保指定行的列数与当前表头一致
    ensureRowColumnCount(rowIndex) {
      if (rowIndex >= 0 && rowIndex < this.dataRows.length) {
        const row = this.dataRows[rowIndex]
        const targetColumnCount = this.currentColumnCount

        // 如果当前行的列数少于目标列数，补充列
        while (row.length < targetColumnCount) {
          row.push({
            content: '',
            isEditing: false,
            originalContent: '',
            hasMath: false,
            selectAll: false
          })
        }

        // 如果当前行的列数多于目标列数，移除多余的列
        if (row.length > targetColumnCount) {
          row.splice(targetColumnCount)
        }
      }
    },

    // 确保所有现有行的列数与当前表头一致
    ensureAllRowsColumnCount() {
      for (let i = 0; i < this.dataRows.length; i++) {
        this.ensureRowColumnCount(i)
      }
    },

    // 外部调用：设置单元格内容
    setCellContent(rowIndex, cellIndex, content) {
      this.ensureRowExists(rowIndex)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.content = content || ''
        cell.hasMath = this.containsMath(content)

        const cellRef = this.$refs[`cell-${rowIndex}-${cellIndex}`]
        if (cellRef && cellRef[0]) {
          cellRef[0].setContent(content)
        }
      }
    },

    // === JSON数据插入功能 ===

    /**
     * 从JSON数据插入表格数据
     * @param {Object} jsonData - JSON数据对象
     * @param {Object} options - 插入选项
     */
    insertDataFromJSON(jsonData, options = {}) {
      try {
        const {
          clearExisting = false,
          startRow = 0,
          mergeCells = [],
          validateData = true
        } = options

        // 验证JSON数据格式
        if (validateData && !this.validateJSONData(jsonData)) {
          throw new Error('JSON数据格式不正确')
        }

        // 清空现有数据（如果需要）
        if (clearExisting) {
          this.clearAllData()
        }

        // 插入数据行（优先使用cellRows格式）
        if (jsonData.cellRows && Array.isArray(jsonData.cellRows)) {
          this.insertCellRowsFromJSON(jsonData.cellRows, startRow)
        } else if (jsonData.rows && Array.isArray(jsonData.rows)) {
          this.insertRowsFromJSON(jsonData.rows, startRow)
        }

        // 应用单元格合并
        if (mergeCells && mergeCells.length > 0) {
          this.applyCellMerges(mergeCells)
        }

        // 强制更新表格以确保样式正确应用
        this.$nextTick(() => {
          this.$forceUpdate()
          // 再次确保合并单元格样式正确
          this.$nextTick(() => {
            this.forceUpdateAllMergedCells()
          })
        })

        // 更新表格信息
        this.$emit('table-updated')
        this.$emit('data-inserted', { jsonData, options })

        return {
          success: true,
          message: '数据插入成功',
          insertedRows: jsonData.rows ? jsonData.rows.length : 0
        }

      } catch (error) {
        console.error('JSON数据插入失败:', error)
        return {
          success: false,
          message: error.message || '数据插入失败',
          error: error
        }
      }
    },

    /**
     * 验证JSON数据格式
     * @param {Object} jsonData - 要验证的JSON数据
     * @returns {boolean} 验证结果
     */
    validateJSONData(jsonData) {
      if (!jsonData || typeof jsonData !== 'object') {
        return false
      }

      // 检查是否有cellRows数组（新格式）或rows数组（旧格式）
      if (jsonData.cellRows && Array.isArray(jsonData.cellRows)) {
        // 验证cellRows格式
        return this.validateCellRowsData(jsonData.cellRows)
      } else if (jsonData.rows && Array.isArray(jsonData.rows)) {
        // 验证rows格式
        return this.validateRowsData(jsonData.rows)
      }

      return false
    },

    /**
     * 验证简单rows数据格式
     */
    validateRowsData(rows) {
      for (const row of rows) {
        if (!Array.isArray(row) || row.length > this.currentColumnCount) {
          return false
        }

        // 检查每个单元格数据
        for (const cell of row) {
          if (cell !== null && cell !== undefined && typeof cell !== 'string' && typeof cell !== 'number') {
            return false
          }
        }
      }
      return true
    },

    /**
     * 验证复杂cellRows数据格式
     */
    validateCellRowsData(cellRows) {
      for (const row of cellRows) {
        if (!Array.isArray(row) || row.length > this.currentColumnCount) {
          return false
        }

        // 检查每个单元格数据对象
        for (const cellData of row) {
          if (!cellData || typeof cellData !== 'object') {
            return false
          }

          // content字段是必需的
          if (!cellData.hasOwnProperty('content')) {
            return false
          }

          // 验证可选字段的类型
          if (cellData.hasMath !== undefined && typeof cellData.hasMath !== 'boolean') {
            return false
          }
          if (cellData.width !== undefined && typeof cellData.width !== 'number') {
            return false
          }
          if (cellData.height !== undefined && typeof cellData.height !== 'number') {
            return false
          }
        }
      }
      return true
    },

    /**
     * 从JSON数据插入行
     * @param {Array} rows - 行数据数组
     * @param {number} startRow - 开始插入的行索引
     */
    insertRowsFromJSON(rows, startRow = 0) {
      rows.forEach((rowData, index) => {
        const targetRowIndex = startRow + index
        this.ensureRowExists(targetRowIndex)

        // 填充行数据，确保每行的单元格数与当前表头一致
        for (let cellIndex = 0; cellIndex < this.currentColumnCount; cellIndex++) {
          const cellValue = cellIndex < rowData.length ? rowData[cellIndex] : ''
          const content = cellValue !== null && cellValue !== undefined ? String(cellValue) : ''

          const cell = this.dataRows[targetRowIndex][cellIndex]
          if (cell) {
            // 使用Vue.set确保响应式更新
            this.$set(cell, 'content', content)
            this.$set(cell, 'hasMath', this.containsMath(content))
            this.$set(cell, 'isEditing', false)
            this.$set(cell, 'originalContent', content)
            // 清除可能存在的合并标记
            if (cell.merged) {
              this.$delete(cell, 'merged')
            }
          }
        }
      })
    },

    /**
     * 从JSON数据插入复杂格式的行（支持公式和宽度配置）
     * @param {Array} cellRows - 单元格数据数组
     * @param {number} startRow - 开始插入的行索引
     */
    insertCellRowsFromJSON(cellRows, startRow = 0) {
      cellRows.forEach((rowData, index) => {
        const targetRowIndex = startRow + index
        this.ensureRowExists(targetRowIndex)

        // 填充行数据，确保每行的单元格数与当前表头一致
        for (let cellIndex = 0; cellIndex < this.currentColumnCount; cellIndex++) {
          const cellData = cellIndex < rowData.length ? rowData[cellIndex] : {}
          const content = cellData.content || ''

          const cell = this.dataRows[targetRowIndex][cellIndex]
          if (cell) {
            // 使用Vue.set确保响应式更新
            this.$set(cell, 'content', content)
            this.$set(cell, 'hasMath', cellData.hasMath || this.containsMath(content))
            this.$set(cell, 'isEditing', false)
            this.$set(cell, 'originalContent', content)

            // 设置公式相关属性
            if (cellData.mathML) {
              this.$set(cell, 'mathML', cellData.mathML)
            }
            if (cellData.hasMultipleContent) {
              this.$set(cell, 'hasMultipleContent', cellData.hasMultipleContent)
            }
            if (cellData.mathMLMap) {
              this.$set(cell, 'mathMLMap', cellData.mathMLMap)
            }

            // 设置尺寸属性（如果有的话）
            if (cellData.width) {
              this.$set(cell, 'width', cellData.width)
            }
            if (cellData.height) {
              this.$set(cell, 'height', cellData.height)
            }

            // 清除可能存在的合并标记
            if (cell.merged) {
              this.$delete(cell, 'merged')
            }
          }
        }
      })

      console.log('复杂格式数据插入完成，行数:', cellRows.length)
    },

    /**
     * 应用单元格合并
     * @param {Array} mergeCells - 合并单元格配置数组
     */
    applyCellMerges(mergeCells) {
      this.$nextTick(() => {
        const table = this.$refs.editableTable
        if (!table) return

        mergeCells.forEach(merge => {
          try {
            const {
              startRow,
              startCol,
              endRow,
              endCol,
              content = ''
            } = merge

            // 验证合并范围
            if (!this.validateMergeRange(startRow, startCol, endRow, endCol)) {
              console.warn('无效的合并范围:', merge)
              return
            }

            // 应用合并
            this.mergeCellRange(startRow, startCol, endRow, endCol, content)

          } catch (error) {
            console.error('应用单元格合并失败:', error, merge)
          }
        })

        // 在所有合并应用完成后，强制更新整个表格
        this.$nextTick(() => {
          this.forceUpdateAllMergedCells()
        })
      })
    },

    /**
     * 验证合并范围
     * @param {number} startRow - 开始行
     * @param {number} startCol - 开始列
     * @param {number} endRow - 结束行
     * @param {number} endCol - 结束列
     * @returns {boolean} 验证结果
     */
    validateMergeRange(startRow, startCol, endRow, endCol) {
      return (
        typeof startRow === 'number' && startRow >= 0 &&
        typeof startCol === 'number' && startCol >= 0 && startCol < this.currentColumnCount &&
        typeof endRow === 'number' && endRow >= startRow &&
        typeof endCol === 'number' && endCol >= startCol && endCol < this.currentColumnCount
      )
    },

    /**
     * 合并单元格范围
     * @param {number} startRow - 开始行
     * @param {number} startCol - 开始列
     * @param {number} endRow - 结束行
     * @param {number} endCol - 结束列
     * @param {string} content - 合并后的内容
     */
    mergeCellRange(startRow, startCol, endRow, endCol, content = '') {
      const table = this.$refs.editableTable
      if (!table) return

      // 确保所有相关行都存在
      for (let row = startRow; row <= endRow; row++) {
        this.ensureRowExists(row)
      }

      // 计算合并的行数和列数
      const rowspan = endRow - startRow + 1
      const colspan = endCol - startCol + 1

      // 获取目标单元格
      const targetRowIndex = startRow
      const targetRow = table.rows[targetRowIndex]

      if (!targetRow) return

      const targetCell = targetRow.cells[startCol]
      if (!targetCell) return

      // 设置合并属性
      if (rowspan > 1) {
        targetCell.setAttribute('rowspan', rowspan)
      }
      if (colspan > 1) {
        targetCell.setAttribute('colspan', colspan)
      }

      // 设置内容
      if (content) {
        this.setCellContent(startRow, startCol, content)
      }

      // 隐藏被合并的单元格
      for (let row = startRow; row <= endRow; row++) {
        for (let col = startCol; col <= endCol; col++) {
          if (row === startRow && col === startCol) {
            continue // 跳过主单元格
          }

          const cellRowIndex = row
          const cellRow = table.rows[cellRowIndex]
          if (cellRow && cellRow.cells[col]) {
            cellRow.cells[col].style.display = 'none'
          }
        }
      }

      // 标记单元格已合并
      const mainCell = this.dataRows[startRow][startCol]
      if (mainCell) {
        // 使用Vue.set确保响应式更新
        this.$set(mainCell, 'merged', {
          rowspan,
          colspan,
          startRow,
          startCol,
          endRow,
          endCol
        })
      }

      // 强制重新渲染以应用样式
      this.$nextTick(() => {
        this.forceUpdateMergedCellStyles(startRow, startCol, endRow, endCol)
      })
    },

    /**
     * 强制更新合并单元格样式
     * @param {number} startRow - 开始行
     * @param {number} startCol - 开始列
     * @param {number} endRow - 结束行
     * @param {number} endCol - 结束列
     */
    forceUpdateMergedCellStyles(startRow, startCol, endRow, endCol) {
      const table = this.$refs.editableTable
      if (!table) return

      // 获取主合并单元格
      const targetRowIndex = startRow
      const targetRow = table.rows[targetRowIndex]
      if (!targetRow) return

      const targetCell = targetRow.cells[startCol]
      if (!targetCell) return

      // 强制添加合并单元格的CSS类
      if (!targetCell.classList.contains('merged-cell')) {
        targetCell.classList.add('merged-cell')
      }

      // 确保rowspan和colspan属性正确设置
      const rowspan = endRow - startRow + 1
      const colspan = endCol - startCol + 1

      if (rowspan > 1) {
        targetCell.setAttribute('rowspan', rowspan)
      }
      if (colspan > 1) {
        targetCell.setAttribute('colspan', colspan)
      }

      // 强制重新计算样式
      targetCell.style.display = 'none'
      targetCell.offsetHeight // 触发重排
      targetCell.style.display = ''

      console.log('强制更新合并单元格样式:', {
        startRow, startCol, endRow, endCol,
        rowspan, colspan,
        hasClass: targetCell.classList.contains('merged-cell')
      })
    },

    /**
     * 强制更新所有合并单元格
     */
    forceUpdateAllMergedCells() {
      // 遍历所有数据行，找到合并单元格并强制更新
      this.dataRows.forEach((row, rowIndex) => {
        row.forEach((cell, cellIndex) => {
          if (cell && cell.merged) {
            const { startRow, startCol, endRow, endCol } = cell.merged
            this.forceUpdateMergedCellStyles(startRow, startCol, endRow, endCol)
          }
        })
      })

      // 强制Vue重新渲染
      this.$forceUpdate()

      console.log('强制更新所有合并单元格完成')
    },

    /**
     * 清空所有数据
     */
    clearAllData() {
      this.dataRows.splice(0, this.dataRows.length)

      // 添加一个空行
      const emptyRow = Array(this.currentColumnCount).fill(null).map(() => ({
        content: '',
        isEditing: false,
        originalContent: '',
        hasMath: false,
        selectAll: false
      }))
      this.dataRows.push(emptyRow)

      // 清除所有合并
      this.clearAllMerges()
    },

    /**
     * 清除所有单元格合并
     */
    clearAllMerges() {
      this.$nextTick(() => {
        const table = this.$refs.editableTable
        if (!table) return

        // 重置所有数据单元格的合并属性
        const dataCells = table.querySelectorAll('.editable-cell')
        dataCells.forEach(cell => {
          cell.removeAttribute('rowspan')
          cell.removeAttribute('colspan')
          cell.style.display = ''
        })

        // 清除数据中的合并标记
        this.dataRows.forEach(row => {
          row.forEach(cell => {
            if (cell.merged) {
              delete cell.merged
            }
          })
        })
      })
    },

    /**
     * 获取表格数据为JSON格式
     * @param {Object} options - 导出选项
     * @returns {Object} JSON格式的表格数据
     */
    getDataAsJSON(options = {}) {
      const {
        includeEmpty = false,
        includeMergeInfo = true
      } = options

      const result = {
        rows: [],
        merges: [],
        headers: this.currentHeaderConfig.headers,
        headerMerges: [],
        headerWidthConfig: this.currentHeaderWidthConfig,
        metadata: {
          totalRows: this.dataRows.length,
          totalColumns: this.currentColumnCount,
          headerRows: this.currentHeaderConfig.headers.length,
          exportTime: new Date().toISOString(),
          useDynamicHeader: this.internalUseDynamicHeader || this.useDynamicHeader,
          title: this.internalHeaderConfig?.title || '检验记录表'
        }
      }

      // 处理表头合并信息，添加宽度和高度
      if (includeMergeInfo && this.currentHeaderConfig.merges) {
        this.currentHeaderConfig.merges.forEach(merge => {
          const { startRow, startCol, endRow, endCol } = merge
          const rowspan = endRow - startRow + 1
          const colspan = endCol - startCol + 1

          // 计算合并单元格的宽度
          let totalWidth = 0
          for (let i = startCol; i <= endCol && i < this.currentColumnWidths.length; i++) {
            totalWidth += this.currentColumnWidths[i] || this.headerCellWidth
          }

          // 计算合并单元格的高度
          let totalHeight = 0
          if (this.currentHeaderWidthConfig && this.currentHeaderWidthConfig.headerHeights) {
            // 使用动态表头配置中的行高度
            for (let i = startRow; i <= endRow; i++) {
              totalHeight += this.currentHeaderWidthConfig.headerHeights[i] || this.headerCellHeight || 50
            }
          } else {
            // 使用默认行高
            const baseHeight = this.headerCellHeight || 50
            totalHeight = baseHeight * rowspan
          }

          result.headerMerges.push({
            startRow,
            startCol,
            endRow,
            endCol,
            rowspan,
            colspan,
            width: totalWidth,
            height: totalHeight,
            content: merge.content || ''
          })
        })
      } else if (this.currentHeaderConfig.merges) {
        // 如果不包含合并信息，直接复制原始数据
        result.headerMerges = [...this.currentHeaderConfig.merges]
      }

      // 导出行数据
      this.dataRows.forEach((row, rowIndex) => {
        const rowData = row.map(cell => cell.content || '')

        // 如果不包含空行，跳过完全空的行
        if (!includeEmpty && rowData.every(content => !content.trim())) {
          return
        }

        result.rows.push(rowData)

        // 收集合并信息
        if (includeMergeInfo) {
          row.forEach((cell, cellIndex) => {
            if (cell.merged) {
              const { startRow, startCol, endRow, endCol, rowspan, colspan } = cell.merged

              // 计算合并单元格的宽度
              let totalWidth = 0
              for (let i = startCol; i <= endCol && i < this.currentColumnWidths.length; i++) {
                totalWidth += this.currentColumnWidths[i] || this.headerCellWidth
              }

              // 计算合并单元格的高度
              let totalHeight = 0
              if (this.currentHeaderWidthConfig && this.currentHeaderWidthConfig.headerHeights) {
                // 使用动态表头配置中的行高度
                for (let i = startRow; i <= endRow; i++) {
                  totalHeight += this.currentHeaderWidthConfig.headerHeights[i] || this.headerCellHeight || 50
                }
              } else {
                // 使用默认行高
                const baseHeight = this.headerCellHeight || 50
                totalHeight = baseHeight * rowspan
              }

              result.merges.push({
                startRow,
                startCol,
                endRow,
                endCol,
                rowspan,
                colspan,
                width: totalWidth,
                height: totalHeight,
                content: cell.content || ''
              })
            }
          })
        }
      })

      return result
    }
  }
}
</script>

<style scoped>
/* 表格容器样式 */
.table-container-wrapper {
  position: relative;
}

.table-wrapper {
  width: 100%;
  overflow: auto;
}

.table-container {
  border: 2px solid #ddd;
  border-radius: 8px;
  overflow: auto;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-scroll-container {
  overflow: auto;
  max-height: 100%;
}

table {
  border-collapse: collapse;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  table-layout: fixed;
  width: auto !important;
}

/* 表头样式 */
.header-cell {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #ddd;
  padding: 12px 8px;
  text-align: center;
  font-weight: bold;
  color: #333;
  white-space: nowrap;
  user-select: none;
  cursor: default;
  box-sizing: border-box;
  /* 宽高通过内联样式控制，这里不设置固定值 */
}

/* 纵向文字样式 - 只影响文字方向，不影响单元格布局 */
.header-cell.vertical-text {
  padding: 12px 4px;
}

.header-cell.vertical-text .vertical-text-span {
  writing-mode: vertical-rl;
  text-orientation: upright;
  display: inline-block;
  white-space: nowrap;
  height: 100%;
  line-height: 1;
}

/* 横向文字样式 */
.header-cell.horizontal-text {
  padding: 12px 8px;
}

/* 可编辑单元格样式 */
.editable-cell {
  border: 1px solid #ddd;
  padding: 0;
  text-align: center;
  cursor: text;
  transition: all 0.2s ease;
  min-height: 50px;
  white-space: pre-wrap;
  word-wrap: break-word;
  vertical-align: top;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  /* 宽度通过内联样式控制，这里不设置固定值 */
}

.editable-cell:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
}

.editable-cell.has-math {
  background-color: #e8f5e8;
}

.editable-cell.merged-cell {
  background-color: #f8f9fa !important;
  border: 2px solid #007bff !important;
  font-weight: 500;
  vertical-align: top !important;
  padding: 0 !important;
  position: relative;
}

.editable-cell.merged-cell:hover {
  background-color: #e3f2fd !important;
  border-color: #0056b3 !important;
}

/* 合并单元格中的编辑器样式 */
.editable-cell.merged-cell .cell-editor-wrapper {
  height: 100% !important;
  min-height: 100% !important;
}

.editable-cell.merged-cell .cell-editor {
  height: 100% !important;
  min-height: 100% !important;
  border: none !important;
  border-radius: 0 !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  box-shadow: none !important;
  padding: 8px !important;
}

.editable-cell.merged-cell .cell-editor:focus {
  background-color: #ffffff !important;
  box-shadow: inset 0 0 0 1px rgba(0, 123, 255, 0.3) !important;
}

.editable-cell.merged-cell .cell-display {
  height: 100% !important;
  min-height: 100% !important;
  padding: 8px !important;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 150px;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.context-menu-item.delete-item {
  color: #dc3545;
}

.context-menu-item.delete-item:hover {
  background-color: #f8d7da;
}

.context-menu-divider {
  height: 1px;
  background-color: #eee;
  margin: 4px 0;
}

.context-menu-info {
  padding: 8px 16px;
  font-size: 12px;
  color: #666;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.context-menu-info .info-label {
  font-weight: 500;
}

.context-menu-info .info-value {
  font-weight: bold;
  color: #007bff;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 8px;
  padding: 20px;
  min-width: 300px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dialog h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.dialog-content {
  margin-bottom: 20px;
}

.dialog-content label {
  display: block;
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.dialog-content input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn-cancel, .btn-confirm {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-cancel {
  background: #6c757d;
  color: white;
}

.btn-confirm {
  background: #007bff;
  color: white;
}

.btn-cancel:hover {
  background: #5a6268;
}

.btn-confirm:hover {
  background: #0056b3;
}
</style>
